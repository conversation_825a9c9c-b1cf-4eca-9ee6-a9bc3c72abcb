import { IsBoolean, IsDate, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { HistoricalChat, Information, QueryCopilotRequest, SchemaInfo, TimeContext, Context, CopilotAnswer } from '../proto/reporting/copilot.pb';
import { Type } from 'class-transformer';
import { TransformProtoTimestampToDate } from '@tangopay/shared/utils/timestamp-converter';

class TimeContextDto implements TimeContext {
  @IsString()
  type: string;

  @IsString()
  startDate: string;

  @IsString()
  endDate: string;
}

class InformationDto implements Information {
  @IsString()
  type: string;

  @IsString()
  name: string;
}

class SchemaInfoDto implements SchemaInfo {
  @IsString()
  ctes: string[];

  @Type(() => TimeContextDto)
  timeContext: TimeContextDto;

  @IsString()
  queryType: string;

  @ValidateNested({ each: true })
  @Type(() => InformationDto)
  options: InformationDto[];

  @IsBoolean()
  hasMissingInformation: boolean;

  @ValidateNested({ each: true })
  @Type(() => InformationDto)
  missingInformation: InformationDto[];

  @ValidateNested({ each: true })
  @Type(() => InformationDto)
  itemsToQuery: InformationDto[];
}

class CopilotAnswerDto implements CopilotAnswer {
  @IsString()
  message: string;

  @IsString()
  UI: string;

  data: { [key: string]: any };

  @IsString()
  UIType: string;
}

class ContextDto implements Context {
  @ValidateNested()
  @Type(() => SchemaInfoDto)
  schemaInfo: SchemaInfoDto;

  decomposition: { [key: string]: any };

  @IsString()
  SQLQuery: string;

  validation: { [key: string]: any };
}

class HistoricalChatDto implements Omit<HistoricalChat, 'createdAt'> {
  @IsString()
  query: string;

  @IsUUID()
  businessId: string;

  @ValidateNested()
  @Type(() => CopilotAnswerDto)
  answer: CopilotAnswerDto;

  @TransformProtoTimestampToDate()
  @IsDate()
  createdAt: Date;

  @ValidateNested()
  @Type(() => ContextDto)
  context: ContextDto;
}

export class QueryCopilotRequestDto implements Omit<QueryCopilotRequest, 'historicalChat'> {
  @IsString()
  query: string;

  @IsUUID()
  businessId: string;

  @ValidateNested({ each: true })
  @Type(() => HistoricalChatDto)
  historicalChat: HistoricalChatDto[];

  @IsOptional()
  @IsString()
  metadata?: string;
}
