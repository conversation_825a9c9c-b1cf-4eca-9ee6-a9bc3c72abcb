import { IsBoolean, IsDate, Is<PERSON>ptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { HistoricalChat, Information, QueryCopilotRequest, SchemaInfo, TimeContext } from '../proto/reporting/copilot.pb';
import { Type } from 'class-transformer';
import { Timestamp } from 'typeorm';

class TimeContextDto implements TimeContext {
  @IsString()
  type: string;

  @IsString()
  startDate: string;

  @IsString()
  endDate: string;
}

class InformationDto implements Information {
  @IsString()
  type: string;

  @IsString()
  name: string;
}

class SchemaInfoDto implements SchemaInfo {
  @IsString()
  ctes: string[];

  @Type(() => TimeContextDto)
  timeContext: TimeContextDto;

  @IsString()
  queryType: string;

  @ValidateNested({ each: true })
  @Type(() => InformationDto)
  options: InformationDto[];

  @IsBoolean()
  hasMissingInformation: boolean;

  @ValidateNested({ each: true })
  @Type(() => InformationDto)
  missingInformation: InformationDto[];

  @ValidateNested({ each: true })
  @Type(() => InformationDto)
  itemsToQuery: InformationDto[];
}

class ContextDto implements Context {
  @Type(() => SchemaInfoDto)
  schemaInfo: SchemaInfoDto;

  @IsString()
  SQLQuery: string;
}

class HistoricalChatDto implements HistoricalChat {
  @IsString()
  query: string;

  @IsUUID()
  businessId: string;

  @ValidateNested()
  @Type(() => CopilotAnswerDto)
  answer: CopilotAnswerDto;

  @IsDate()
  createdAt: Timestamp;

  @ValidateNested()
  @Type(() => ContextDto)
  context: ContextDto;
}

export class QueryCopilotRequestDto implements QueryCopilotRequest {
  @IsString()
  query: string;

  @IsUUID()
  businessId: string;

  @ValidateNested({ each: true })
  @Type(() => HistoricalChatDto)
  historicalChat: HistoricalChatDto[];

  @IsOptional()
  @IsString()
  metadata?: string;
}
